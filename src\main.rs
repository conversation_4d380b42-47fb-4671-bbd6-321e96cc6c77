use crate::{
    block::cube::create_cube_mesh,
    world::{
        level::{LevelData, load_level},
        load_world,
    },
};
use bevy::{
    diagnostic::{
        EntityCountDiagnosticsPlugin, FrameTimeDiagnosticsPlugin, LogDiagnosticsPlugin,
        SystemInformationDiagnosticsPlugin,
    },
    input::mouse::AccumulatedMouseMotion,
    prelude::*,
};
use std::f32::consts::FRAC_PI_2;
mod block;
mod prelude;
mod world;

fn main() {
    App::new()
        .insert_resource(ClearColor(Color::srgb(0.5, 0.5, 0.9)))
        .add_plugins((
            DefaultPlugins.set(ImagePlugin::default_nearest()),
            // Adds a system that prints diagnostics to the console.
            // The other diagnostics plugins can still be used without this if you want to use them in an ingame overlay for example.
            LogDiagnosticsPlugin::default(),
            // Adds frame time, FPS and frame count diagnostics.
            FrameTimeDiagnosticsPlugin::default(),
            // Adds an entity count diagnostic.
            EntityCountDiagnosticsPlugin,
            // Adds cpu and memory usage diagnostics for systems and the entire game process.
            SystemInformationDiagnosticsPlugin,
        ))
        .insert_resource(load_level())
        .add_systems(Startup, setup)
        .add_systems(Startup, load_world)
        .add_systems(Update, move_camera)
        .run();
}

/// set up a simple 3D scene
fn setup(
    mut commands: Commands,
    asset_server: Res<AssetServer>,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    // circular base
    commands.spawn((
        Mesh3d(meshes.add(Circle::new(4.0))),
        MeshMaterial3d(materials.add(Color::WHITE)),
        Transform::from_rotation(Quat::from_rotation_x(-std::f32::consts::FRAC_PI_2)),
    ));

    // cube
    let custom_texture_handle: Handle<Image> = asset_server.load("dirt.png");

    commands.spawn((
        Mesh3d(meshes.add(create_cube_mesh())),
        MeshMaterial3d(materials.add(StandardMaterial {
            base_color_texture: Some(custom_texture_handle),
            ..default()
        })),
        Transform::from_xyz(0.0, 0.5, 0.0),
    ));

    // cube
    // for x in -199..200 {
    //     for z in -199..200 {
    //         commands.spawn((
    //             Mesh3d(meshes.add(Cuboid::new(0.9, 0.9, 0.9))),
    //             MeshMaterial3d(materials.add(Color::srgb_u8(124, 144, 255))),
    //             Transform::from_xyz(x as f32, 0.5, z as f32),
    //         ));
    //     }
    // }
    // light
    commands.spawn((
        PointLight {
            shadows_enabled: true,
            ..default()
        },
        Transform::from_xyz(4.0, 8.0, 4.0),
    ));
    // camera
    commands.spawn((
        Camera3d::default(),
        Transform::from_xyz(-2.5, 4.5, 9.0).looking_at(Vec3::ZERO, Vec3::Y),
    ));
}

fn move_camera(
    accumulated_mouse_motion: Res<AccumulatedMouseMotion>,
    keyboard_input: Res<ButtonInput<KeyCode>>,
    camera: Single<&mut Transform, With<Camera>>,
) {
    const SPEED: f32 = 0.2;
    const SENSITIVITY: Vec2 = Vec2::new(0.003, 0.002);

    let mut transform = camera.into_inner();
    let delta = accumulated_mouse_motion.delta;

    if delta != Vec2::ZERO {
        // Note that we are not multiplying by delta_time here.
        // The reason is that for mouse movement, we already get the full movement that happened since the last frame.
        // This means that if we multiply by delta_time, we will get a smaller rotation than intended by the user.
        // This situation is reversed when reading e.g. analog input from a gamepad however, where the same rules
        // as for keyboard input apply. Such an input should be multiplied by delta_time to get the intended rotation
        // independent of the framerate.
        let delta_yaw = -delta.x * SENSITIVITY.x;
        let delta_pitch = -delta.y * SENSITIVITY.y;

        let (yaw, pitch, roll) = transform.rotation.to_euler(EulerRot::YXZ);
        let yaw = yaw + delta_yaw;

        // If the pitch was ±¹⁄₂ π, the camera would look straight up or down.
        // When the user wants to move the camera back to the horizon, which way should the camera face?
        // The camera has no way of knowing what direction was "forward" before landing in that extreme position,
        // so the direction picked will for all intents and purposes be arbitrary.
        // Another issue is that for mathematical reasons, the yaw will effectively be flipped when the pitch is at the extremes.
        // To not run into these issues, we clamp the pitch to a safe range.
        const PITCH_LIMIT: f32 = FRAC_PI_2 - 0.01;
        let pitch = (pitch + delta_pitch).clamp(-PITCH_LIMIT, PITCH_LIMIT);

        transform.rotation = Quat::from_euler(EulerRot::YXZ, yaw, pitch, roll);
    }

    // Horizontal movement
    if keyboard_input.pressed(KeyCode::KeyW) {
        let dir = transform.forward();
        let vec = Vec3::new(dir.x, 0.0, dir.z) * SPEED;

        transform.translation += vec;
    }
    if keyboard_input.pressed(KeyCode::KeyS) {
        let dir = transform.back();
        let vec = Vec3::new(dir.x, 0.0, dir.z) * SPEED;

        transform.translation += vec;
    }
    if keyboard_input.pressed(KeyCode::KeyA) {
        let dir = transform.left();
        let vec = Vec3::new(dir.x, 0.0, dir.z) * SPEED;

        transform.translation += vec;
    }
    if keyboard_input.pressed(KeyCode::KeyD) {
        let dir = transform.right();
        let vec = Vec3::new(dir.x, 0.0, dir.z) * SPEED;

        transform.translation += vec;
    }

    // Vertical movement
    if keyboard_input.pressed(KeyCode::Space) {
        transform.translation.y += SPEED;
    }
    if keyboard_input.pressed(KeyCode::ShiftLeft) {
        transform.translation.y -= SPEED;
    }
}
