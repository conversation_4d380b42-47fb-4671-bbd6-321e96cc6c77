use crate::block::cube::create_cube_mesh;
use crate::block::types::BlockType;
use crate::prelude::{CHUNK_SIZE, CHUNK_VOLUME};
use bevy::asset::RenderAssetUsages;
use bevy::math::IVec3;
use bevy::render::mesh::{Mesh, PrimitiveTopology};

#[derive(Clone, Copy)]
pub struct Chunk {
    pub id: IVec3,
    pub blocks: [BlockType; CHUNK_VOLUME],
}

impl Chunk {
    pub fn new(id: IVec3) -> Self {
        Self {
            id,
            blocks: [BlockType::default(); CHUNK_VOLUME],
        }
    }

    pub fn get_block(&self, pos: IVec3) -> BlockType {
        self.blocks[pos.x as usize
            + pos.y as usize * CHUNK_SIZE
            + pos.z as usize * CHUNK_SIZE * CHUNK_SIZE]
    }

    pub fn set_block(&mut self, pos: IVec3, block: BlockType) {
        self.blocks[pos.x as usize
            + pos.y as usize * CHUNK_SIZE
            + pos.z as usize * CHUNK_SIZE * CHUNK_SIZE] = block;
    }

    pub fn create_mesh(&self) -> Mesh {
        let mut mesh = Mesh::new(
            PrimitiveTopology::TriangleList,
            RenderAssetUsages::MAIN_WORLD | RenderAssetUsages::RENDER_WORLD,
        );

        for x in 0..CHUNK_SIZE {
            for y in 0..CHUNK_SIZE {
                for z in 0..CHUNK_SIZE {
                    let block = self.get_block(IVec3::new(x as i32, y as i32, z as i32));

                    if block == BlockType::Air {
                        continue;
                    }

                    
                }
            }
        }

        mesh
    }
}
