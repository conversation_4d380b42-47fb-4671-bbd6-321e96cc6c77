use crate::{
    block::{cube::create_cube_mesh, types::BlockType},
    prelude::CHUNK_SIZE,
    world::{chunk::Chunk, level::LevelData},
};
use bevy::prelude::*;

pub(crate) mod chunk;
pub(crate) mod level;

pub fn load_world(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    let mut chunk = Chunk::new(IVec3::new(0, 0, 0));

    for x in 0..CHUNK_SIZE {
        for y in 0..CHUNK_SIZE {
            for z in 0..CHUNK_SIZE {
                // let custom_texture_handle: Handle<Image> = asset_server.load("dirt.png");

                // commands.spawn((
                //     Mesh3d(meshes.add(create_cube_mesh())),
                //     MeshMaterial3d(materials.add(StandardMaterial {
                //         base_color_texture: Some(custom_texture_handle),
                //         ..default()
                //     })),
                //     Transform::from_xyz(0.0, 0.5, 0.0),
                // ));

                // chunk.set_block(IVec3::new(x as i32, y as i32, z as i32), BlockType::Stone);
            }
        }
    }

    // commands.spawn((
    //     Mesh3d(meshes.add(chunk.create_mesh())),
    //     MeshMaterial3d(materials.add(Color::srgb_u8(124, 144, 255))),
    //     Transform::from_xyz(0.0, 0.0, 0.0),
    // ));
}
